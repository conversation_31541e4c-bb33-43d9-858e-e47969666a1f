import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';

import 'settings_screen.dart';
import 'dart:async';
import '../utils/persian_utils.dart';
import 'main_navigation.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  Timer? _levelUpTimer;

  // Stable English keys for database storage and logic
  final Map<String, double> _moodScores = {
    'happy': 5.0,
    'relaxed': 4.0,
    'neutral': 3.0,
    'tired': 2.0,
    'angry': 1.0,
  };

  final Map<String, Color> _moodColors = {
    'happy': Colors.green,
    'relaxed': Colors.blue,
    'neutral': Colors.grey,
    'tired': Colors.orange,
    'angry': Colors.red,
  };

  final Map<String, String> _moodEmojis = {
    'happy': '😊',
    'relaxed': '😌',
    'neutral': '😐',
    'tired': '😓',
    'angry': '😡',
  };

  // Persian display strings for UI
  final Map<String, String> _moodDisplayNames = {
    'happy': 'خوشحال',
    'relaxed': 'آرام',
    'neutral': 'خنثی',
    'tired': 'خسته',
    'angry': 'عصبانی',
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _levelUpTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final userTitle = appState.getUserTitle();
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: screenSize.width,
        height: screenSize.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF87CEEB), Colors.white],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAppBar(context),
                    SizedBox(height: screenSize.height * 0.02),
                    _buildUserHeader(context, userTitle, appState),
                    SizedBox(height: screenSize.height * 0.03),
                    _buildUserStats(context, appState),
                    SizedBox(height: screenSize.height * 0.03),
                    _buildNotificationSection(context, appState),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'پروفایل',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          IconButton(
            icon: const Icon(FeatherIcons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
            splashRadius: 24,
            color: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildUserHeader(
      BuildContext context, String userTitle, AppState appState) {
    final screenSize = MediaQuery.of(context).size;
    final profileSize = screenSize.width * 0.2; // Responsive profile image size

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.06),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      width: profileSize,
                      height: profileSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.primaryColor,
                          width: 2,
                        ),
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          appState.getProfileImagePath(),
                          fit: BoxFit.cover,
                          width: profileSize,
                          height: profileSize,
                        ),
                      ),
                    ),
                  );
                },
              ),
              SizedBox(width: screenSize.width * 0.05),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userTitle,
                      style: TextStyle(
                        fontSize:
                            22 * MediaQuery.textScalerOf(context).scale(1.0),
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textColor,
                      ),
                    ),
                    SizedBox(height: screenSize.height * 0.005),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${PersianUtils.toPersianNumber(appState.points)} امتیاز',
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppTheme.lightTextColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: screenSize.height * 0.02),
          const Divider(thickness: 1),
          SizedBox(height: screenSize.height * 0.01),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                  context,
                  PersianUtils.toPersianNumber(appState.dailyStreak),
                  'رکورد روزانه',
                  Icons.calendar_today),
              _buildStatItem(
                  context,
                  PersianUtils.toPersianNumber(
                      appState.completedChallenges.length),
                  'چالش‌ها',
                  Icons.emoji_events),
              _buildStatItem(
                  context,
                  PersianUtils.toPersianNumber(appState.points),
                  'امتیاز کل',
                  Icons.star),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
      BuildContext context, String value, String label, IconData icon) {
    final screenSize = MediaQuery.of(context).size;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(screenSize.width * 0.025),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppTheme.primaryColor,
            size: screenSize.width * 0.06,
          ),
        ),
        SizedBox(height: screenSize.height * 0.008),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16 * MediaQuery.textScalerOf(context).scale(1.0),
          ),
        ),
        SizedBox(height: screenSize.height * 0.004),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12 * MediaQuery.textScalerOf(context).scale(1.0),
          ),
        ),
      ],
    );
  }

  Widget _buildUserStats(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;

    return Column(
      children: [
        // Points and Challenges Section
        Container(
          padding: EdgeInsets.all(screenSize.width * 0.06),
          margin: EdgeInsets.all(screenSize.width * 0.04),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'چالش‌های تکمیل شده',
                    style: const TextStyle(
                      fontFamily: 'Samim',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2F4F4F),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color:
                          AppTheme.primaryColor.withAlpha(26), // 0.1 * 255 ≈ 26
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${PersianUtils.toPersianNumber(appState.completedChallenges.length)}/${PersianUtils.toPersianNumber(appState.challengeRequirements.length)}',
                      style: GoogleFonts.lato(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenSize.height * 0.02),
              appState.completedChallenges.isEmpty
                  ? _buildEmptyChallengeState(context)
                  : Column(
                      children: List.generate(
                        appState.completedChallenges.length,
                        (index) {
                          final challengeId =
                              appState.completedChallenges[index];
                          final challengeName =
                              appState.challengeDescriptions[challengeId] ??
                                  'Unknown Challenge';
                          return _buildChallengeItem(context, challengeName,
                              _getChallengeIcon(challengeId), true);
                        },
                      ),
                    ),
              SizedBox(height: screenSize.height * 0.02),
              Text(
                'پیشرفت چالش‌ها',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2F4F4F),
                ),
              ),
              SizedBox(height: screenSize.height * 0.02),
              SizedBox(
                height: screenSize.height * 0.18,
                child: _buildChallengeProgressList(context, appState),
              ),
              SizedBox(height: screenSize.height * 0.02),
              Text(
                'چطوری سریع‌تر رشد کنم؟',
                style: GoogleFonts.lato(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2F4F4F),
                ),
              ),
              SizedBox(height: screenSize.height * 0.02),
              _buildAdvancementTip(context, appState),
            ],
          ),
        ),

        // Mood Analysis Section
        Container(
          padding: EdgeInsets.all(screenSize.width * 0.06),
          margin: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(FeatherIcons.refreshCw, size: 18),
                    onPressed: () {
                      // Refresh mood data
                      appState.fetchMoodData();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('در حال به‌روزرسانی داده‌های حال و هوا...'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                    splashRadius: 20,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              SizedBox(height: screenSize.height * 0.005),
              _buildMoodAnalysis(context),
            ],
          ),
        ),

        SizedBox(height: screenSize.height * 0.04),
      ],
    );
  }

  Widget _buildMoodAnalysis(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final screenSize = MediaQuery.of(context).size;

    if (appState.isLoadingMoods) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (appState.moodData.isEmpty) {
      return _buildEmptyMoodState(context);
    }

    return Column(
      children: [
        _buildWeeklyMoodTrendChart(context, appState),
        SizedBox(height: screenSize.height * 0.03),
        _buildEmotionalMapChart(context, appState),
        SizedBox(height: screenSize.height * 0.03),
        _buildAramCoachMessage(context, appState),
      ],
    );
  }

  Widget _buildEmptyMoodState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mood,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'هنوز داده‌ای ثبت نشده است',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'تمرینات تنفسی را کامل کنید تا الگوهای روحیه‌تان را ردیابی کنید',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontFamily: 'Samim',
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              // Navigate to Home tab instead of using Navigator.pop()
              NavigationHelper.switchToHomeTab();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text(
              'شروع تمرین',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMoodEmoji(String mood) {
    return _moodEmojis[mood] ?? '😐';
  }

  // Helper function to get Persian display name for mood
  String _getMoodDisplayName(String moodKey) {
    return _moodDisplayNames[moodKey] ?? moodKey;
  }

  // Helper method to convert numerical mood scores to descriptive Persian text
  String _getMoodDescriptionForScore(double score) {
    if (score >= 4.5) return "عالی";
    if (score >= 3.8) return "خیلی خوب";
    if (score >= 3.2) return "خوب";
    if (score >= 2.5) return "معمولی";
    if (score >= 1.8) return "کمی خسته";
    if (score > 0) return "پریشان";
    return ""; // Empty string for zero/negative scores (no tooltip)
  }

  // Helper method to format date strings for chart labels

  Widget _buildWeeklyMoodTrendChart(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;

    // Persian weekday abbreviations (Saturday to Friday)
    final List<String> persianWeekdays = ['ش', 'ی', 'د', 'س', 'چ', 'پ', 'ج'];

    // Calculate Persian week (Saturday to Friday)
    final now = DateTime.now();

    // Inside _buildWeeklyMoodTrendChart method
    final int daysSinceSaturday = (now.weekday + 1) % 7;
    final DateTime startOfWeek =
        now.subtract(Duration(days: daysSinceSaturday));

    List<BarChartGroupData> barGroups = [];

    // Generate bars for each day of the Persian week (Saturday to Friday)
    barGroups = List.generate(7, (index) {
      final date = startOfWeek.add(Duration(days: index));

      // Find moods for this specific day
      final dayMoods = appState.moodData.where((mood) {
        final moodDate = DateTime.parse(mood['timestamp']);
        return moodDate.year == date.year &&
            moodDate.month == date.month &&
            moodDate.day == date.day;
      }).toList();

      // Calculate average mood score for the day
      double avgScore = 0.0; // Default to 0 for empty days (no bar visible)
      if (dayMoods.isNotEmpty) {
        double totalScore = 0;
        for (var mood in dayMoods) {
          totalScore += _moodScores[mood['mood']] ?? 3.0;
        }
        avgScore = totalScore / dayMoods.length;
      }

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: avgScore,
            color: avgScore > 0
                ? _getColorForMoodScore(avgScore)
                : Colors.transparent,
            width: screenSize.width * 0.08,
            borderRadius: BorderRadius.circular(4),
            gradient: avgScore > 0
                ? LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      _getColorForMoodScore(avgScore)
                          .withAlpha((0.6 * 255).toInt()),
                      _getColorForMoodScore(avgScore),
                    ],
                  )
                : null,
          ),
        ],
      );
    });

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.05 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(screenSize.width * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سفر آرامش شما در این هفته',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
          SizedBox(height: screenSize.height * 0.02),
          SizedBox(
            height: screenSize.height * 0.3,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 5,
                minY: 0,
                barTouchData: BarTouchData(
                  enabled: true,
                  touchTooltipData: BarTouchTooltipData(
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      group.x.toInt();
                      final moodScore = rod.toY;

                      // Get descriptive text for the mood score
                      final moodDescription =
                          _getMoodDescriptionForScore(moodScore);

                      // Return null (no tooltip) for days with zero/negative scores
                      if (moodDescription.isEmpty) {
                        return null;
                      }

                      return BarTooltipItem(
                        moodDescription,
                        const TextStyle(
                          fontFamily: 'Samim',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < persianWeekdays.length) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 3),
                            child: Text(
                              persianWeekdays[index],
                              style: const TextStyle(
                                fontFamily: 'Samim',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2F4F4F),
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      reservedSize: 36,
                      showTitles: true,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        String emoji = '';
                        switch (value.toInt()) {
                          case 1:
                            emoji = _moodEmojis['angry'] ?? '';
                            break;
                          case 2:
                            emoji = _moodEmojis['tired'] ?? '';
                            break;
                          case 3:
                            emoji = _moodEmojis['neutral'] ?? '';
                            break;
                          case 4:
                            emoji = _moodEmojis['relaxed'] ?? '';
                            break;
                          case 5:
                            emoji = _moodEmojis['happy'] ?? '';
                            break;
                        }
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Text(
                            emoji,
                            style: const TextStyle(fontSize: 18),
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: false),
                gridData: const FlGridData(show: false),
                barGroups: barGroups,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get color based on mood score
  Color _getColorForMoodScore(double score) {
    if (score >= 4.5) return _moodColors['happy'] ?? Colors.green;
    if (score >= 3.5) return _moodColors['relaxed'] ?? Colors.blue;
    if (score >= 2.5) return _moodColors['neutral'] ?? Colors.grey;
    if (score >= 1.5) return _moodColors['tired'] ?? Colors.orange;
    return _moodColors['angry'] ?? Colors.red;
  }

  Widget _buildEmotionalMapChart(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;

    // Calculate mood distribution
    Map<String, int> moodCounts = {};
    for (var item in appState.moodData) {
      final mood = item['mood'] as String;
      moodCounts[mood] = (moodCounts[mood] ?? 0) + 1;
    }

    // Create pie chart sections
    List<PieChartSectionData> sections = [];
    int touchedIndex = -1;

    final moodOrder = ['happy', 'relaxed', 'neutral', 'tired', 'angry'];

    for (int i = 0; i < moodOrder.length; i++) {
      final mood = moodOrder[i];
      final count = moodCounts[mood] ?? 0;

      if (count > 0) {
        final percentage = (count / appState.moodData.length) * 100;
        final isTouch = i == touchedIndex;

        sections.add(
          PieChartSectionData(
            color: _moodColors[mood] ?? Colors.grey,
            value: percentage,
            title: isTouch
                ? '${PersianUtils.toPersianNumbers(percentage.toStringAsFixed(1))}%'
                : '',
            radius: isTouch ? screenSize.width * 0.12 : screenSize.width * 0.1,
            titleStyle: TextStyle(
              fontFamily: 'Samim',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black.withAlpha((0.5 * 255).toInt()),
                  blurRadius: 2,
                ),
              ],
            ),
            badgeWidget: isTouch
                ? Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha((0.2 * 255).toInt()),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    child: Text(
                      _getMoodEmoji(mood),
                      style: const TextStyle(fontSize: 16),
                    ),
                  )
                : null,
            badgePositionPercentageOffset: 1.2,
          ),
        );
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.05 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(screenSize.width * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نقشه احساسی شما',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
          SizedBox(height: screenSize.height * 0.02),
          Row(
            children: [
              // Pie Chart
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: screenSize.height * 0.2,
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      return PieChart(
                        PieChartData(
                          pieTouchData: PieTouchData(
                            touchCallback:
                                (FlTouchEvent event, pieTouchResponse) {
                              setState(() {
                                if (!event.isInterestedForInteractions ||
                                    pieTouchResponse == null ||
                                    pieTouchResponse.touchedSection == null) {
                                  touchedIndex = -1;
                                  return;
                                }
                                touchedIndex = pieTouchResponse
                                    .touchedSection!.touchedSectionIndex;
                              });
                            },
                          ),
                          borderData: FlBorderData(show: false),
                          sectionsSpace: 2,
                          centerSpaceRadius: screenSize.width * 0.08,
                          sections: sections,
                        ),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(width: screenSize.width * 0.04),
              // Legend
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: moodOrder
                      .where((mood) => (moodCounts[mood] ?? 0) > 0)
                      .map((mood) {
                    final count = moodCounts[mood] ?? 0;
                    final percentage = (count / appState.moodData.length) * 100;

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: _moodColors[mood] ?? Colors.grey,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${_getMoodEmoji(mood)} ${_getMoodDisplayName(mood)}',
                                  style: const TextStyle(
                                    fontFamily: 'Samim',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '${PersianUtils.toPersianNumbers(percentage.toStringAsFixed(1))}%',
                                  style: TextStyle(
                                    fontFamily: 'Samim',
                                    fontSize: 10,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAramCoachMessage(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;
    final message = _getDynamicInsightMessage(appState);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4A90E2),
            const Color(0xFF357ABD),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A90E2).withAlpha((0.3 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(screenSize.width * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha((0.2 * 255).toInt()),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.lightbulb_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'پیام مربی آرام برای تو',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: screenSize.height * 0.015),
          Text(
            message,
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 14,
              color: Colors.white,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  String _getDynamicInsightMessage(AppState appState) {
    if (appState.moodData.isEmpty) {
      return 'خوش آمدی! هر بار که بعد از تمرین احساست را ثبت کنی، به ما کمک می‌کنی تا راهنمای بهتری برای سفر آرامشت باشیم.';
    }

    if (appState.moodData.length < 3) {
      return 'خوش آمدی! هر بار که بعد از تمرین احساست را ثبت کنی، به ما کمک می‌کنی تا راهنمای بهتری برای سفر آرامشت باشیم.';
    }

    // Calculate dominant mood
    Map<String, int> moodCounts = {};
    for (var item in appState.moodData) {
      final mood = item['mood'] as String;
      moodCounts[mood] = (moodCounts[mood] ?? 0) + 1;
    }

    String dominantMood = '';
    int maxCount = 0;
    moodCounts.forEach((mood, count) {
      if (count > maxCount) {
        maxCount = count;
        dominantMood = mood;
      }
    });

    // Calculate recent trend (last 5 entries)
    if (appState.moodData.length >= 5) {
      final recentMoods = appState.moodData.sublist(0, 5);
      double avgRecent = 0;
      for (var item in recentMoods) {
        final mood = item['mood'] as String;
        avgRecent += _moodScores[mood] ?? 3.0;
      }
      avgRecent /= recentMoods.length;

      // Check for positive trend
      if (avgRecent > 3.8) {
        return 'چه پیشرفت فوق‌العاده‌ای! نمودار هفتگی شما نشان می‌دهد که حالتان بهتر شده. به همین مسیر ادامه دهید!';
      }
    }

    // Check for stress/anger dominance
    if (dominantMood == 'angry' || dominantMood == 'tired') {
      return 'به نظر می‌رسد هفته چالش‌برانگیزی را پشت سر گذاشته‌اید. پیشنهاد می‌کنیم «تنفس ۴-۷-۸» را برای آرامش بیشتر امتحان کنی.';
    }

    // Check for good streak (mostly happy/relaxed)
    final positiveCount =
        (moodCounts['happy'] ?? 0) + (moodCounts['relaxed'] ?? 0);
    final positivePercentage = (positiveCount / appState.moodData.length) * 100;

    if (positivePercentage > 60) {
      return 'ثبات شما در تمرین کردن تحسین‌برانگیز است و نتایج آن در نقشه احساسی شما کاملاً مشخص است. شما بیشتر روزها آرام و خوشحال بوده‌اید. آفرین!';
    }

    // Default encouraging message
    return 'تمرین منظم تنفس شما در حال ایجاد تغییرات مثبت است. با ادامه این مسیر، به تعادل بیشتری خواهید رسید.';
  }

  Widget _buildEmptyChallengeState(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: screenSize.width * 0.12,
            color: Colors.grey[400],
          ),
          SizedBox(height: screenSize.height * 0.02),
          Text(
            'هنوز هیچ چالشی تکمیل نشده است',
            style: TextStyle(
              fontFamily: 'Samim',
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          SizedBox(height: screenSize.height * 0.008),
          Text(
            'تمرینات را به طور مداوم تکمیل کنید تا چالش‌ها را کسب کنید',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Samim',
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
          SizedBox(height: screenSize.height * 0.02),
        ],
      ),
    );
  }

  Widget _buildChallengeItem(
      BuildContext context, String name, IconData icon, bool completed) {
    final screenSize = MediaQuery.of(context).size;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: screenSize.height * 0.01),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(screenSize.width * 0.025),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: screenSize.width * 0.06,
            ),
          ),
          SizedBox(width: screenSize.width * 0.04),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (completed)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildChallengeProgressList(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;

    // Get all challenge IDs that haven't been completed yet
    final incompleteChallenges = appState.challengeRequirements.keys
        .where((id) => !appState.completedChallenges.contains(id))
        .toList();

    if (incompleteChallenges.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: screenSize.width * 0.09,
              color: Colors.amber,
            ),
            SizedBox(height: screenSize.height * 0.012),
            Text(
              'همه چالش‌ها تکمیل شد!',
              style: TextStyle(
                fontFamily: 'Samim',
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: incompleteChallenges.length,
      itemBuilder: (context, index) {
        final challengeId = incompleteChallenges[index];
        final challengeName =
            appState.challengeDescriptions[challengeId] ?? 'Unknown Challenge';
        final progress = appState.getChallengeProgressSync(challengeId);

        return _buildChallengeProgressCard(
          context,
          challengeName,
          _getChallengeIcon(challengeId),
          progress['progress'],
          progress['target'],
        );
      },
    );
  }

  Widget _buildChallengeProgressCard(
    BuildContext context,
    String name,
    IconData icon,
    int progress,
    int target,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final double percentage =
        target > 0 ? (progress / target).clamp(0.0, 1.0) : 0.0;

    return Container(
      width: screenSize.width * 0.35,
      margin: EdgeInsets.only(right: screenSize.width * 0.03),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withAlpha((0.1 * 255).toInt())),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((0.1 * 255).toInt()),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(screenSize.width * 0.03),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(screenSize.width * 0.02),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppTheme.primaryColor,
                size: screenSize.width * 0.055,
              ),
            ),
            SizedBox(height: screenSize.height * 0.01),
            Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const Spacer(),
            Text(
              target > 0
                  ? '${PersianUtils.toPersianNumber(progress)}/${PersianUtils.toPersianNumber(target)}'
                  : 'در حال انجام',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: screenSize.height * 0.006),
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: LinearProgressIndicator(
                value: percentage,
                backgroundColor: Colors.grey.withAlpha((0.1 * 255).toInt()),
                minHeight: 6,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancementTip(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;
    String nextTitle;
    String requirement;

    if (appState.points < 100) {
      nextTitle = "جویای سکون";
      requirement =
          "فقط ${PersianUtils.toPersianNumber(100 - appState.points)} امتیاز دیگه تا این عنوان!";
    } else if (appState.points < 250 ||
        appState.completedChallenges.length < 2) {
      nextTitle = "استاد درون";
      if (appState.points < 250 && appState.completedChallenges.length < 2) {
        requirement =
            "به ${PersianUtils.toPersianNumber(250 - appState.points)} امتیاز و ${PersianUtils.toPersianNumber(2 - appState.completedChallenges.length)} چالش دیگه نیاز داری.";
      } else if (appState.points < 250) {
        requirement =
            "${PersianUtils.toPersianNumber(250 - appState.points)} امتیاز بیشتر کسب کنید";
      } else {
        requirement =
            "${PersianUtils.toPersianNumber(2 - appState.completedChallenges.length)} چالش بیشتر تکمیل کنید";
      }
    } else if (appState.points < 500 ||
        appState.completedChallenges.length < 4) {
      nextTitle = "مرشد جان";
      if (appState.points < 500 && appState.completedChallenges.length < 4) {
        requirement =
            "به ${PersianUtils.toPersianNumber(500 - appState.points)} امتیاز و ${PersianUtils.toPersianNumber(4 - appState.completedChallenges.length)} چالش دیگه نیاز داری.";
      } else if (appState.points < 500) {
        requirement =
            "${PersianUtils.toPersianNumber(500 - appState.points)} امتیاز بیشتر کسب کنید";
      } else {
        requirement =
            "${PersianUtils.toPersianNumber(4 - appState.completedChallenges.length)} چالش بیشتر تکمیل کنید";
      }
    } else {
      nextTitle = "مرشد جان";
      requirement = "شما به بالاترین عنوان رسیده‌اید!";
    }

    return Card(
      elevation: 0,
      color: Colors.blue[50],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(screenSize.width * 0.04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هدف بعدی: رسیدن به $nextTitle',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16 * MediaQuery.textScalerOf(context).scale(1.0),
              ),
            ),
            SizedBox(height: screenSize.height * 0.008),
            Text(
              requirement,
              style: TextStyle(
                fontSize: 14 * MediaQuery.textScalerOf(context).scale(1.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getChallengeIcon(String challengeId) {
    switch (challengeId) {
      case 'streak_7':
        return Icons.date_range;
      case 'streak_30':
        return Icons.calendar_month;
      case 'all_styles':
        return Icons.animation;
      case 'all_exercises':
        return Icons.fitness_center;
      case 'exercises_10':
        return Icons.ten_k;
      case 'exercises_50':
        return Icons.confirmation_number;
      case 'level_2':
        return Icons.upgrade;
      default:
        return Icons.emoji_events;
    }
  }

  Widget _buildNotificationSection(BuildContext context, AppState appState) {
    final screenSize = MediaQuery.of(context).size;

    return Container(
      padding: EdgeInsets.all(screenSize.width * 0.06),
      margin: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'اعلان‌ها',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2F4F4F),
                ),
              ),
              Switch(
                value: appState.notificationsEnabledInApp,
                onChanged: (value) async {
                  if (value) {
                    // User is trying to enable notifications
                    final success = await appState.toggleNotifications();
                    if (success && context.mounted) {
                      if (appState.reminderTime == null) {
                        _showNotificationTimePicker(context, appState);
                      }
                    } else if (!success && context.mounted) {
                      _showPermissionDeniedDialog(context, appState);
                    }
                  } else {
                    // User is disabling notifications
                    await appState.toggleNotifications();
                  }
                },
                activeColor: AppTheme.primaryColor,
              ),
            ],
          ),
          SizedBox(height: screenSize.height * 0.02),
          // Show status text when app setting is enabled but permissions not granted
          if (appState.notificationsEnabledInApp &&
              appState.notificationPermissionStatus !=
                  NotificationPermissionStatus.granted)
            _buildPermissionStatusText(context, appState, screenSize),
          if (appState.notificationsEnabledInApp &&
              appState.notificationPermissionStatus ==
                  NotificationPermissionStatus.granted)
            _buildReminderTimeSetting(context, appState),
        ],
      ),
    );
  }

  Widget _buildReminderTimeSetting(BuildContext context, AppState appState) {
    return InkWell(
      onTap: () => _showNotificationTimePicker(context, appState),
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.access_time,
                color: AppTheme.primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 15),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'یادآوری روزانه',
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  appState.reminderTime ??
                      'تنظیم نشده - برای تنظیم زمان ضربه بزنید',
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationTimePicker(
      BuildContext context, AppState appState) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: _parseTimeOfDay(appState.reminderTime) ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              onSurface: AppTheme.textColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null && context.mounted) {
      final formattedTime = _formatTimeOfDay(pickedTime);
      appState.setReminderTime(formattedTime);

      // Show a loading indicator while scheduling notification
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      try {
        // Schedule notification for this time
        await appState.scheduleNotification();

        // Close loading dialog
        if (context.mounted) Navigator.of(context).pop();

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'یادآوری روزانه برای $formattedTime تنظیم شد',
                style: const TextStyle(fontFamily: 'Samim'),
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        // Close loading dialog
        if (context.mounted) Navigator.of(context).pop();

        String errorMessage = 'خطا در تنظیم یادآوری';

        // Handle specific error cases
        if (e.toString().contains('exact_alarms_not_permitted')) {
          errorMessage =
              'یادآوری‌ها ممکن است به دلیل محدودیت‌های سیستم دقیق نباشند. همچنان کار خواهند کرد، اما زمان‌بندی ممکن است کمی متفاوت باشد.';

          // Still show partial success message since we're using inexact alarms as fallback
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                duration: const Duration(seconds: 4),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: 'تنظیمات',
                  textColor: Colors.white,
                  onPressed: () {
                    // Navigate to the settings screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SettingsScreen(),
                      ),
                    );
                  },
                ),
              ),
            );
          }
        } else {
          // Show error message for other errors
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('$errorMessage: ${e.toString()}'),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    }
  }

  TimeOfDay? _parseTimeOfDay(String? timeString) {
    if (timeString == null) return null;

    // Convert Persian numerals to English for parsing
    final englishTimeString = PersianUtils.toEnglishNumbers(timeString);

    // Parse time like "14:30" (24-hour format)
    final timeParts = englishTimeString.split(':');
    if (timeParts.length != 2) return null;

    final int hour = int.tryParse(timeParts[0]) ?? 0;
    final int minute = int.tryParse(timeParts[1]) ?? 0;

    // Validate hour and minute ranges
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) return null;

    return TimeOfDay(hour: hour, minute: minute);
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    final timeString = '$hour:$minute';
    return PersianUtils.toPersianNumbers(timeString);
  }

  Widget _buildPermissionStatusText(
      BuildContext context, AppState appState, Size screenSize) {
    String statusText;
    Color statusColor;
    IconData statusIcon;
    VoidCallback? onTap;

    switch (appState.notificationPermissionStatus) {
      case NotificationPermissionStatus.notDetermined:
        statusText = 'برای درخواست دسترسی اعلان‌ها کلید را فشار دهید';
        statusColor = Colors.blue;
        statusIcon = Icons.info_outline;
        break;
      case NotificationPermissionStatus.denied:
        statusText =
            'دسترسی رد شد - برای تلاش مجدد یا بررسی تنظیمات ضربه بزنید';
        statusColor = Colors.orange;
        statusIcon = Icons.warning_outlined;
        onTap = () => _showPermissionDeniedDialog(context, appState);
        break;
      case NotificationPermissionStatus.permanentlyDenied:
        statusText = 'برای استفاده از یادآوری‌ها در تنظیمات گوشی فعال کنید';
        statusColor = Colors.red;
        statusIcon = Icons.settings;
        onTap = () => appState.openNotificationSettings();
        break;
      case NotificationPermissionStatus.granted:
        return const SizedBox.shrink(); // Don't show anything when granted
    }

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.04,
        vertical: screenSize.height * 0.01,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          child: Row(
            children: [
              Icon(
                statusIcon,
                color: statusColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  statusText,
                  style: GoogleFonts.lato(
                    color: statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: statusColor,
                  size: 12,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPermissionDeniedDialog(BuildContext context, AppState appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دسترسی اعلان‌ها'),
        content: const Text(
          'برای ارسال یادآوری‌های تنفس، دسترسی اعلان‌ها مورد نیاز است. '
          'می‌توانید آن را در تنظیمات گوشی خود فعال کنید یا دوباره درخواست دسترسی کنید.',
          style: TextStyle(fontFamily: 'Samim'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لغو'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await appState.requestNotificationPermissions();
              if (success && context.mounted) {
                _showNotificationTimePicker(context, appState);
              }
            },
            child: const Text('تلاش مجدد'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              appState.openNotificationSettings();
            },
            child: const Text('باز کردن تنظیمات'),
          ),
        ],
      ),
    );
  }
}
